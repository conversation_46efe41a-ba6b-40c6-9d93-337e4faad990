import { PrismaService } from '@/modules/prisma/prisma.service';
import { DoctorProfile } from '@adapters/persistence/database/doctor.persistence';
import { DosespotService } from '@modules/dosespot/dosespot.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class GetPatientForDoctorUseCase {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly dosespotService: DosespotService,
  ) {}

  async execute(patientId: string, doctor: DoctorProfile) {
    const patient = await this.prismaService.patient.findUniqueOrThrow({
      where: {
        id: patientId,
        doctorId: doctor.id,
      },
      select: {
        id: true,
        userId: true,
        doseSpotPatientId: true,
        birthDate: true,
        gender: true,
        height: true,
        weight: true,
        idPhoto: true,
        facePhoto: true,
        identityVerificationType: true,
        lastFourSSN: true,
        vouchedVerifiedAt: true,
        status: true,
        statusBeforeCancellation: true,
        verificationStatus: true,
        rejectedStatus: true,
        rejectedReason: true,
        createdAt: true,
        updatedAt: true,
        acceptedAt: true,
        canceledBy: true,
        canceledAt: true,
        rejectedAt: true,
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            createdAt: true,
          },
        },
        state: {
          select: {
            name: true,
            code: true,
          },
        },
        pharmacy: {
          select: {
            id: true,
            name: true,
            metadata: true,
            color: true,
          },
        },
        desiredTreatments: {
          select: {
            vials: true,
            product: {
              select: {
                id: true,
                name: true,
                image: true,
                metadata: true,
              },
            },
          },
        },
        treatment: {
          select: {
            id: true,
          },
          take: 1,
        },
        shippingAddresses: {
          select: {
            id: true,
            address1: true,
            address2: true,
            city: true,
            zip: true,
          },
        },
        conversations: {
          select: {
            id: true,
            type: true,
            updatedAt: true,
          },
        },
      },
    });

    const ssoUrl = await this.dosespotService.getSSOUrl(
      patient.doseSpotPatientId,
      doctor.doseSpotClinicianId,
    );

    const hasTreatment = patient.treatment.length > 0;

    patient.desiredTreatments = patient.desiredTreatments.map((dt) => {
      dt.product.name = `${dt.product.metadata['label']}, ${dt.product.metadata['form']}`;
      delete dt.product.metadata;
      return dt;
    });

    // Transform conversations array to single conversation for API compatibility
    const patientDoctorConversation = patient.conversations?.find(
      (c) => c.type === 'patientDoctor',
    );
    const doctorAdminconversation = patient.conversations?.find(
      (c) => c.type === 'doctorAdmin',
    );

    const result = {
      ...patient,
      sso: ssoUrl.toString(),
      hasTreatment,
      conversation: patientDoctorConversation || null,
      doctorAdminConversation: doctorAdminconversation || null,
    };

    delete result.doseSpotPatientId;
    delete result.treatment;
    delete result.conversations; // Remove plural conversations

    return result;
  }
}
