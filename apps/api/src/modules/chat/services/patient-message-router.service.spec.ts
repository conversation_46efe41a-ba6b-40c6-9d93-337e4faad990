import { AiService } from '@modules/ai/ai.service';
import { IntercomService } from '@modules/intercom/intercom.service';
import { PrismaService } from '@modules/prisma/prisma.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';

import {
  ConversationHistoryWithRouting,
  PatientMessageRouterService,
} from './patient-message-router.service';

describe('PatientMessageRouterService - Historical Content Bug Fix', () => {
  let service: PatientMessageRouterService;
  let aiService: jest.Mocked<AiService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PatientMessageRouterService,
        {
          provide: PrismaService,
          useValue: {},
        },
        {
          provide: AiService,
          useValue: {
            structuredPromptWithToolUse: jest.fn(),
          },
        },
        {
          provide: IntercomService,
          useValue: {},
        },
        {
          provide: EventEmitter2,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<PatientMessageRouterService>(
      PatientMessageRouterService,
    );
    aiService = module.get(AiService);
  });

  describe('validateMessageSummary', () => {
    it('should detect and sanitize summary containing historical date references', () => {
      const conversationHistory: ConversationHistoryWithRouting = {
        messages: [
          {
            user: 'patient',
            message: 'I had side effects last month',
            date: '2024-01-15T10:00:00Z',
            id: 'old-msg-1',
          },
          {
            user: 'doctor',
            message: 'Let me know if you have any concerns',
            date: '2024-01-16T10:00:00Z',
            id: 'old-msg-2',
          },
          {
            user: 'patient',
            message: 'I need a refill please',
            date: '2024-02-15T10:00:00Z',
            id: 'new-msg-1',
          },
        ],
        previousRoutings: [],
        hasActiveIntercomConversation: false,
        lastIntercomId: null,
      };

      const messageIdsToAnalyze = ['new-msg-1'];
      const summaryWithHistoricalContent =
        'The patient is requesting a refill, following up on the side effects they mentioned last month';

      const result = (service as any).validateMessageSummary(
        summaryWithHistoricalContent,
        conversationHistory,
        messageIdsToAnalyze,
      );

      expect(result).not.toContain('last month');
      expect(result).toContain('The patient sent a new message');
    });

    it('should detect and sanitize summary containing exact phrases from historical messages', () => {
      const conversationHistory: ConversationHistoryWithRouting = {
        messages: [
          {
            user: 'patient',
            message: 'I am experiencing severe headaches and nausea',
            date: '2024-01-15T10:00:00Z',
            id: 'old-msg-1',
          },
          {
            user: 'patient',
            message: 'Can you help with billing?',
            date: '2024-02-15T10:00:00Z',
            id: 'new-msg-1',
          },
        ],
        previousRoutings: [],
        hasActiveIntercomConversation: false,
        lastIntercomId: null,
      };

      const messageIdsToAnalyze = ['new-msg-1'];
      const summaryWithHistoricalContent =
        'The patient is asking for billing help, and previously mentioned experiencing severe headaches and nausea';

      const result = (service as any).validateMessageSummary(
        summaryWithHistoricalContent,
        conversationHistory,
        messageIdsToAnalyze,
      );

      expect(result).not.toContain('severe headaches and nausea');
      expect(result).toContain('Can you help with billing?');
    });

    it('should allow valid summary that only references new messages', () => {
      const conversationHistory: ConversationHistoryWithRouting = {
        messages: [
          {
            user: 'patient',
            message: 'I had issues before',
            date: '2024-01-15T10:00:00Z',
            id: 'old-msg-1',
          },
          {
            user: 'patient',
            message: 'I need help with my current prescription',
            date: '2024-02-15T10:00:00Z',
            id: 'new-msg-1',
          },
        ],
        previousRoutings: [],
        hasActiveIntercomConversation: false,
        lastIntercomId: null,
      };

      const messageIdsToAnalyze = ['new-msg-1'];
      const validSummary =
        'The patient is requesting help with their current prescription';

      const result = (service as any).validateMessageSummary(
        validSummary,
        conversationHistory,
        messageIdsToAnalyze,
      );

      expect(result).toBe(validSummary);
    });

    it('should detect historical phrases like "previously mentioned"', () => {
      const conversationHistory: ConversationHistoryWithRouting = {
        messages: [
          {
            user: 'patient',
            message: 'Old message content',
            date: '2024-01-15T10:00:00Z',
            id: 'old-msg-1',
          },
          {
            user: 'patient',
            message: 'New question about dosage',
            date: '2024-02-15T10:00:00Z',
            id: 'new-msg-1',
          },
        ],
        previousRoutings: [],
        hasActiveIntercomConversation: false,
        lastIntercomId: null,
      };

      const messageIdsToAnalyze = ['new-msg-1'];
      const summaryWithHistoricalPhrase =
        'The patient is asking about dosage, as discussed in our previous conversation';

      const result = (service as any).validateMessageSummary(
        summaryWithHistoricalPhrase,
        conversationHistory,
        messageIdsToAnalyze,
      );

      expect(result).not.toContain('as discussed');
      expect(result).toContain('New question about dosage');
    });
  });

  describe('analyzePatientMessages with improved prompt', () => {
    it('should call AI service with updated prompt that emphasizes new messages only', async () => {
      const conversationHistory: ConversationHistoryWithRouting = {
        messages: [
          {
            user: 'patient',
            message: 'Old concern about side effects',
            date: '2024-01-15T10:00:00Z',
            id: 'old-msg-1',
          },
          {
            user: 'patient',
            message: 'I need a refill',
            date: '2024-02-15T10:00:00Z',
            id: 'new-msg-1',
          },
        ],
        previousRoutings: [],
        hasActiveIntercomConversation: false,
        lastIntercomId: null,
      };

      const messageIdsToAnalyze = ['new-msg-1'];

      // Mock AI response that would previously include historical content
      aiService.structuredPromptWithToolUse.mockResolvedValue({
        relevantForDoctor: false,
        relevantForPatientServices: true,
        reason: 'Refill request',
        inquiryTypes: ['PRESCRIPTION_RENEWAL'],
        relevantMessageIndices: [1],
        messageSummary: 'The patient is requesting a refill',
        continuationConfidence: 0,
      });

      await service.analyzePatientMessages(
        conversationHistory,
        messageIdsToAnalyze,
      );

      // Verify the prompt contains the new instructions
      const calledPrompt =
        aiService.structuredPromptWithToolUse.mock.calls[0][1];
      expect(calledPrompt).toContain(
        'ONLY describe the content of the NEW messages',
      );
      expect(calledPrompt).toContain(
        'Do NOT include any information, references, or content from the "CONVERSATION HISTORY"',
      );
      expect(calledPrompt).toContain(
        'CRITICAL: Only summarize the NEW messages being analyzed',
      );
    });
  });
});
